#!/usr/bin/env python3
"""
Test script to verify inventory quantity reduction fixes
"""

import os
import sys
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.abspath('.'))

from app import create_app, db
from app.modules.inventory.models_product import Product
from app.modules.inventory.models_ingredient import Ingredient
from app.modules.inventory.models_recipe import Recipe, RecipeItem
from app.modules.pos.models_sale import Sale, SaleItem, SaleStatus
from app.modules.auth.models import User
from flask_login import login_user

def test_inventory_reduction():
    """Test inventory quantity reduction for both simple and recipe-based products"""
    
    app = create_app()
    
    with app.app_context():
        print("🧪 Test de la réduction des quantités d'inventaire")
        print("=" * 60)
        
        # Get a test user (assuming there's at least one user)
        user = User.query.first()
        if not user:
            print("❌ Aucun utilisateur trouvé. Veuillez créer un utilisateur d'abord.")
            return False
        
        print(f"👤 Utilisateur de test: {user.username}")
        
        # Test 1: Simple product stock reduction
        print("\n📦 Test 1: Produit simple")
        print("-" * 30)
        
        simple_product = Product.query.filter_by(
            owner_id=user.id,
            has_recipe=False
        ).first()
        
        if not simple_product:
            print("❌ Aucun produit simple trouvé")
        else:
            print(f"Produit: {simple_product.name}")
            initial_stock = simple_product.stock_quantity
            print(f"Stock initial: {initial_stock}")
            
            # Test stock reduction
            test_quantity = 2
            print(f"Test de réduction: {test_quantity} unités")
            
            success = simple_product.update_stock(
                test_quantity, 
                operation='subtract', 
                reason='TEST', 
                reference='TEST-001'
            )
            
            if success:
                db.session.commit()
                print(f"✅ Stock après réduction: {simple_product.stock_quantity}")
                print(f"Différence: {initial_stock - simple_product.stock_quantity}")
                
                # Restore stock
                simple_product.update_stock(
                    test_quantity, 
                    operation='add', 
                    reason='TEST_RESTORE', 
                    reference='TEST-001-RESTORE'
                )
                db.session.commit()
                print(f"🔄 Stock restauré: {simple_product.stock_quantity}")
            else:
                print("❌ Échec de la réduction de stock")
        
        # Test 2: Recipe-based product stock reduction
        print("\n🍳 Test 2: Produit avec recette")
        print("-" * 30)
        
        recipe_product = Product.query.filter_by(
            owner_id=user.id,
            has_recipe=True
        ).first()
        
        if not recipe_product or not recipe_product.recipe:
            print("❌ Aucun produit avec recette trouvé")
        else:
            print(f"Produit: {recipe_product.name}")
            print("Ingrédients avant test:")
            
            ingredient_stocks_before = {}
            for recipe_item in recipe_product.recipe.items:
                ingredient = recipe_item.ingredient
                ingredient_stocks_before[ingredient.id] = ingredient.stock_quantity
                print(f"  - {ingredient.name}: {ingredient.stock_quantity} {ingredient.unit}")
            
            # Test stock reduction
            test_quantity = 1
            print(f"\nTest de réduction: {test_quantity} unité(s)")
            
            success = recipe_product.update_stock(
                test_quantity, 
                operation='subtract', 
                reason='TEST', 
                reference='TEST-002'
            )
            
            if success:
                db.session.commit()
                print("✅ Réduction réussie")
                print("Ingrédients après test:")
                
                for recipe_item in recipe_product.recipe.items:
                    ingredient = recipe_item.ingredient
                    before = ingredient_stocks_before[ingredient.id]
                    after = ingredient.stock_quantity
                    expected_reduction = recipe_item.quantity * test_quantity
                    print(f"  - {ingredient.name}: {before} → {after} (réduction: {before - after}, attendue: {expected_reduction})")
                
                # Restore ingredient stocks
                print("\n🔄 Restauration des stocks d'ingrédients...")
                for recipe_item in recipe_product.recipe.items:
                    ingredient = recipe_item.ingredient
                    ingredient_qty_to_restore = recipe_item.quantity * test_quantity
                    ingredient.update_stock(
                        ingredient_qty_to_restore,
                        operation='add',
                        reason='TEST_RESTORE',
                        reference='TEST-002-RESTORE',
                        commit=False
                    )
                db.session.commit()
                print("✅ Stocks restaurés")
            else:
                print("❌ Échec de la réduction de stock")
        
        # Test 3: Sale workflow simulation
        print("\n🛒 Test 3: Simulation de vente")
        print("-" * 30)
        
        if simple_product:
            print(f"Simulation d'une vente avec {simple_product.name}")
            initial_stock = simple_product.stock_quantity
            print(f"Stock initial: {initial_stock}")
            
            # Create a test sale
            sale = Sale(
                reference=f"TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                owner_id=user.id,
                user_id=user.id,
                status=SaleStatus.PENDING
            )
            db.session.add(sale)
            db.session.flush()  # Get the sale ID
            
            # Add sale item
            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=simple_product.id,
                quantity=1,
                price=simple_product.price,
                total=simple_product.price
            )
            db.session.add(sale_item)
            
            # Simulate stock reduction (as would happen in POS)
            success = simple_product.update_stock(
                1, 
                operation='subtract', 
                reason='SALE', 
                reference=f"Sale #{sale.id}"
            )
            
            if success:
                db.session.commit()
                print(f"✅ Stock après vente: {simple_product.stock_quantity}")
                
                # Simulate sale cancellation
                print("🚫 Simulation d'annulation de vente...")
                success_restore = simple_product.update_stock(
                    1, 
                    operation='add', 
                    reason='SALE_CANCELLED', 
                    reference=f"Cancelled sale #{sale.id}"
                )
                
                if success_restore:
                    sale.status = SaleStatus.CANCELLED
                    db.session.commit()
                    print(f"✅ Stock après annulation: {simple_product.stock_quantity}")
                else:
                    print("❌ Échec de la restauration de stock")
                
                # Clean up test sale
                db.session.delete(sale_item)
                db.session.delete(sale)
                db.session.commit()
                print("🧹 Vente de test supprimée")
            else:
                print("❌ Échec de la réduction de stock pour la vente")
                db.session.rollback()
        
        print("\n" + "=" * 60)
        print("✅ Tests terminés")
        return True

if __name__ == '__main__':
    test_inventory_reduction()
