"""Routes pour le système de notifications"""

from flask import request, jsonify, session, render_template
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from app.modules.notifications import bp
from app.modules.notifications.models import Notification, NotificationStatus, NotificationPreference
from app.modules.notifications.services import NotificationService
from app import db

@bp.route('/api/notifications')
@login_required
def get_notifications():
    """Récupérer les notifications pour l'utilisateur connecté"""
    try:
        # Déterminer le type d'utilisateur
        user_type = 'restaurant'  # Par défaut restaurant owner
        if hasattr(current_user, 'is_customer') and current_user.is_customer:
            user_type = 'customer'
        elif hasattr(current_user, 'is_delivery') and current_user.is_delivery:
            user_type = 'delivery'
        
        # Récupérer les notifications non lues
        notifications = Notification.query.filter_by(
            recipient_type=user_type,
            recipient_id=current_user.id
        ).filter(
            Notification.status.in_([NotificationStatus.PENDING, NotificationStatus.SENT])
        ).order_by(Notification.created_at.desc()).limit(50).all()
        
        return jsonify({
            'success': True,
            'notifications': [notif.to_dict() for notif in notifications],
            'unread_count': len([n for n in notifications if n.status == NotificationStatus.SENT])
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """Marquer une notification comme lue"""
    try:
        notification = Notification.query.get_or_404(notification_id)
        
        # Vérifier que l'utilisateur peut marquer cette notification
        user_type = 'restaurant'
        if hasattr(current_user, 'is_customer') and current_user.is_customer:
            user_type = 'customer'
        elif hasattr(current_user, 'is_delivery') and current_user.is_delivery:
            user_type = 'delivery'
        
        if notification.recipient_type != user_type or notification.recipient_id != current_user.id:
            return jsonify({'success': False, 'error': 'Unauthorized'}), 403
        
        notification.mark_as_read()
        
        return jsonify({'success': True})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """Marquer toutes les notifications comme lues"""
    try:
        user_type = 'restaurant'
        if hasattr(current_user, 'is_customer') and current_user.is_customer:
            user_type = 'customer'
        elif hasattr(current_user, 'is_delivery') and current_user.is_delivery:
            user_type = 'delivery'
        
        notifications = Notification.query.filter_by(
            recipient_type=user_type,
            recipient_id=current_user.id,
            status=NotificationStatus.SENT
        ).all()
        
        for notification in notifications:
            notification.mark_as_read()
        
        return jsonify({'success': True, 'marked_count': len(notifications)})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/preferences')
@login_required
def notification_preferences():
    """Page des préférences de notification"""
    user_type = 'restaurant'
    if hasattr(current_user, 'is_customer') and current_user.is_customer:
        user_type = 'customer'
    elif hasattr(current_user, 'is_delivery') and current_user.is_delivery:
        user_type = 'delivery'
    
    preferences = NotificationPreference.query.filter_by(
        user_id=current_user.id,
        user_type=user_type
    ).first()
    
    if not preferences:
        # Créer des préférences par défaut
        preferences = NotificationPreference(
            user_id=current_user.id,
            user_type=user_type
        )
        db.session.add(preferences)
        db.session.commit()
    
    return render_template('notifications/preferences.html', preferences=preferences)

@bp.route('/preferences', methods=['POST'])
@login_required
def update_notification_preferences():
    """Mettre à jour les préférences de notification"""
    try:
        user_type = 'restaurant'
        if hasattr(current_user, 'is_customer') and current_user.is_customer:
            user_type = 'customer'
        elif hasattr(current_user, 'is_delivery') and current_user.is_delivery:
            user_type = 'delivery'
        
        preferences = NotificationPreference.query.filter_by(
            user_id=current_user.id,
            user_type=user_type
        ).first()
        
        if not preferences:
            preferences = NotificationPreference(
                user_id=current_user.id,
                user_type=user_type
            )
            db.session.add(preferences)
        
        # Mettre à jour les préférences
        preferences.email_enabled = request.form.get('email_enabled') == 'on'
        preferences.browser_enabled = request.form.get('browser_enabled') == 'on'
        preferences.sound_enabled = request.form.get('sound_enabled') == 'on'
        preferences.order_updates = request.form.get('order_updates') == 'on'
        preferences.payment_updates = request.form.get('payment_updates') == 'on'
        preferences.promotional = request.form.get('promotional') == 'on'
        preferences.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'Préférences mises à jour'})
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/test')
@login_required
def test_notification():
    """Route de test pour créer une notification"""
    try:
        service = NotificationService()
        
        # Créer une notification de test
        notification = service.create_notification(
            type='ORDER_RECEIVED',
            title='Nouvelle commande reçue',
            message='Une nouvelle commande a été reçue et attend votre confirmation.',
            recipient_type='restaurant',
            recipient_id=current_user.id,
            data={'test': True}
        )
        
        return jsonify({
            'success': True, 
            'message': 'Notification de test créée',
            'notification_id': notification.id
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
