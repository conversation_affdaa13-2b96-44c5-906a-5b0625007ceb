/**
 * Système de notifications en temps réel
 */

class NotificationManager {
    constructor() {
        this.notifications = [];
        this.unreadCount = 0;
        this.pollInterval = 5000; // 5 secondes
        this.isPolling = false;
        this.soundEnabled = true;
        
        this.init();
    }
    
    init() {
        this.createNotificationArea();
        this.createNotificationBell();
        this.requestNotificationPermission();
        this.startPolling();
        
        // Écouter les événements de visibilité pour optimiser le polling
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopPolling();
            } else {
                this.startPolling();
            }
        });
    }
    
    createNotificationArea() {
        if (document.getElementById('notification-area')) return;
        
        const area = document.createElement('div');
        area.id = 'notification-area';
        area.className = 'position-fixed';
        area.style.cssText = 'top: 20px; right: 20px; z-index: 1050; max-width: 400px;';
        document.body.appendChild(area);
    }
    
    createNotificationBell() {
        const bellContainer = document.querySelector('.notification-bell-container');
        if (!bellContainer) return;
        
        bellContainer.innerHTML = `
            <div class="dropdown">
                <button class="btn btn-outline-secondary position-relative" type="button" 
                        id="notificationDropdown" data-bs-toggle="dropdown">
                    <i class="fas fa-bell"></i>
                    <span id="notification-badge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" 
                          style="display: none;">0</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" style="width: 350px; max-height: 400px; overflow-y: auto;">
                    <li><h6 class="dropdown-header">Notifications</h6></li>
                    <li><hr class="dropdown-divider"></li>
                    <li id="notifications-list">
                        <div class="text-center p-3 text-muted">Aucune notification</div>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <button class="dropdown-item text-center" onclick="notificationManager.markAllAsRead()">
                            <i class="fas fa-check-double"></i> Tout marquer comme lu
                        </button>
                    </li>
                </ul>
            </div>
        `;
    }
    
    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }
    
    startPolling() {
        if (this.isPolling) return;
        
        this.isPolling = true;
        this.pollForNotifications();
        this.pollTimer = setInterval(() => {
            this.pollForNotifications();
        }, this.pollInterval);
    }
    
    stopPolling() {
        this.isPolling = false;
        if (this.pollTimer) {
            clearInterval(this.pollTimer);
        }
    }
    
    async pollForNotifications() {
        try {
            const response = await fetch('/notifications/api/notifications');
            const data = await response.json();
            
            if (data.success) {
                this.updateNotifications(data.notifications);
                this.updateUnreadCount(data.unread_count);
            }
        } catch (error) {
            console.error('Erreur lors de la récupération des notifications:', error);
        }
    }
    
    updateNotifications(newNotifications) {
        // Détecter les nouvelles notifications
        const existingIds = this.notifications.map(n => n.id);
        const reallyNewNotifications = newNotifications.filter(n => !existingIds.includes(n.id));
        
        // Afficher les nouvelles notifications
        reallyNewNotifications.forEach(notification => {
            this.showNotification(notification);
            this.playNotificationSound();
            this.showBrowserNotification(notification);
        });
        
        this.notifications = newNotifications;
        this.updateNotificationsList();
    }
    
    updateUnreadCount(count) {
        this.unreadCount = count;
        const badge = document.getElementById('notification-badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }
    
    updateNotificationsList() {
        const list = document.getElementById('notifications-list');
        if (!list) return;
        
        if (this.notifications.length === 0) {
            list.innerHTML = '<div class="text-center p-3 text-muted">Aucune notification</div>';
            return;
        }
        
        list.innerHTML = this.notifications.slice(0, 10).map(notification => `
            <li>
                <div class="dropdown-item ${notification.status === 'sent' ? 'bg-light' : ''}" 
                     onclick="notificationManager.markAsRead(${notification.id})">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${notification.title}</h6>
                            <p class="mb-1 small">${notification.message}</p>
                            <small class="text-muted">${this.formatDate(notification.created_at)}</small>
                        </div>
                        ${notification.status === 'sent' ? '<span class="badge bg-primary">Nouveau</span>' : ''}
                    </div>
                </div>
            </li>
        `).join('');
    }
    
    showNotification(notification) {
        const area = document.getElementById('notification-area');
        if (!area) return;
        
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-info alert-dismissible fade show mb-2';
        alertDiv.innerHTML = `
            <strong>${notification.title}</strong><br>
            ${notification.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        area.appendChild(alertDiv);
        
        // Auto-remove after 8 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 8000);
    }
    
    showBrowserNotification(notification) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const browserNotification = new Notification(notification.title, {
                body: notification.message,
                icon: '/static/img/logo.png',
                tag: `notification-${notification.id}`
            });
            
            browserNotification.onclick = () => {
                window.focus();
                this.markAsRead(notification.id);
                browserNotification.close();
            };
            
            // Auto-close after 5 seconds
            setTimeout(() => {
                browserNotification.close();
            }, 5000);
        }
    }
    
    playNotificationSound() {
        if (!this.soundEnabled) return;
        
        // Créer un son de notification simple
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
    }
    
    async markAsRead(notificationId) {
        try {
            const response = await fetch(`/notifications/api/notifications/${notificationId}/read`, {
                method: 'POST'
            });
            
            if (response.ok) {
                // Mettre à jour localement
                const notification = this.notifications.find(n => n.id === notificationId);
                if (notification) {
                    notification.status = 'read';
                    this.updateUnreadCount(this.unreadCount - 1);
                    this.updateNotificationsList();
                }
            }
        } catch (error) {
            console.error('Erreur lors du marquage comme lu:', error);
        }
    }
    
    async markAllAsRead() {
        try {
            const response = await fetch('/notifications/api/notifications/mark-all-read', {
                method: 'POST'
            });
            
            if (response.ok) {
                // Mettre à jour localement
                this.notifications.forEach(n => n.status = 'read');
                this.updateUnreadCount(0);
                this.updateNotificationsList();
            }
        } catch (error) {
            console.error('Erreur lors du marquage de toutes comme lues:', error);
        }
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffMins < 1) return 'À l\'instant';
        if (diffMins < 60) return `Il y a ${diffMins} min`;
        if (diffHours < 24) return `Il y a ${diffHours}h`;
        if (diffDays < 7) return `Il y a ${diffDays}j`;
        
        return date.toLocaleDateString('fr-FR');
    }
}

// Initialiser le gestionnaire de notifications quand la page est chargée
let notificationManager;
document.addEventListener('DOMContentLoaded', function() {
    notificationManager = new NotificationManager();
});
